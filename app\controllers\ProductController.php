<?php

class ProductController
{
    private $productModel;
    private $orderModel;
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->productModel = new ProductModel($this->db);
        $this->orderModel = new OrderModel();

        // Get the current action from URL
        $url = isset($_GET['url']) ? explode('/', trim($_GET['url'], '/')) : [];
        $action = isset($url[1]) ? strtolower($url[1]) : 'index';

        // Kiểm tra quyền admin cho các action thêm/sửa/xóa
        if (in_array($action, ['add', 'edit', 'delete'])) {
            $this->requireAdmin();
        }    }

    private function requireAdmin() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => '<PERSON>ạn không có quyền thực hiện thao tác này'
            ];
            header('Location: ' . BASE_URL . '/product');
            exit;
        }
    }

    public function index()
    {
        // Get all products
        $products = $this->productModel->getProducts();
        
        // Load the product list view
        require_once APP_ROOT . '/views/product/list.php';
    }

    public function show($id)
    {
        $product = $this->productModel->getProductById($id);
        if ($product) {
            include 'app/views/product/show.php';
        } else {
            echo "Không thấy sản phẩm.";
        }
    }    public function add()
    {
        $this->requireAdmin();
        $categories = (new CategoryModel($this->db))->getCategories();
        include_once 'app/views/product/add.php';
    }

    public function save()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $price = $_POST['price'] ?? '';
            $category_id = $_POST['category_id'] ?? null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $image = $this->uploadImage($_FILES['image']);
            } else {
                $image = "";
            }            $result = $this->productModel->addProduct($name, $description, $price, $category_id, $image);
            if (is_array($result)) {
                $errors = $result;
                $categories = (new CategoryModel($this->db))->getCategories();
                include APP_ROOT . '/views/product/add.php';
            } else {
                header('Location: ' . BASE_URL . '/product');
            }
        }
    }    public function edit($id)
    {
        $this->requireAdmin();
        $product = $this->productModel->getProductById($id);
        $categories = (new CategoryModel($this->db))->getCategories();
        if ($product) {
            include 'app/views/product/edit.php';
        } else {
            echo "Không thấy sản phẩm.";
        }
    }

    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $description = $_POST['description'];
            $price = $_POST['price'];
            $category_id = $_POST['category_id'];
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                $image = $this->uploadImage($_FILES['image']);
            } else {
                $image = $_POST['existing_image'];
            }            $edit = $this->productModel->updateProduct($id, $name, $description, $price, $category_id, $image);
            if ($edit) {
                header('Location: ' . BASE_URL . '/product');
            } else {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Đã xảy ra lỗi khi lưu sản phẩm.'
                ];
                header('Location: ' . BASE_URL . '/product');
            }
        }
    }    public function delete($id)
    {
        $this->requireAdmin();
        if ($this->productModel->deleteProduct($id)) {
            header('Location: /shopbanhang/Product');
        } else {
            echo "Đã xảy ra lỗi khi xóa sản phẩm.";
        }
    }

    private function uploadImage($file)
    {
        $target_dir = "uploads/";
        if (!is_dir($target_dir)) {
            mkdir($target_dir, 0777, true);
        }
        $target_file = $target_dir . basename($file["name"]);
        $imageFileType = strtolower(pathinfo($target_file, PATHINFO_EXTENSION));
        $check = getimagesize($file["tmp_name"]);
        if ($check === false) {
            throw new Exception("File không phải là hình ảnh.");
        }
        if ($file["size"] > 10 * 1024 * 1024) {
            throw new Exception("Hình ảnh có kích thước quá lớn.");
        }
        if (!in_array($imageFileType, ["jpg", "jpeg", "png", "gif", "webp"])) {
            throw new Exception("Chỉ cho phép các định dạng JPG, JPEG, PNG, GIF và WEBP.");
        }
        if (!move_uploaded_file($file["tmp_name"], $target_file)) {
            throw new Exception("Có lỗi xảy ra khi tải lên hình ảnh.");
        }
        return $target_file;
    }

    /**
     * Thêm sản phẩm vào giỏ hàng
     */    public function addToCart($id)
    {
        // Kiểm tra đăng nhập
        if (!isset($_SESSION['user'])) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng'
            ];            $_SESSION['return_to'] = $_SERVER['HTTP_REFERER'] ?? BASE_URL . '/product';
            header('Location: ' . BASE_URL . '/account/login');
            return;
        }

        try {
            $product = $this->productModel->getProductById($id);
            if (!$product) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Không tìm thấy sản phẩm.'
                ];                $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL . '/product';
                header("Location: $referer");
                return;
            }

            // Khởi tạo giỏ hàng nếu chưa có
            if (!isset($_SESSION['cart'])) {
                $_SESSION['cart'] = [];
            }

            // Thêm hoặc cập nhật số lượng sản phẩm
            if (isset($_SESSION['cart'][$id])) {
                $_SESSION['cart'][$id]['quantity']++;
            } else {
                $_SESSION['cart'][$id] = [
                    'name' => $product->name,
                    'price' => $product->price,
                    'quantity' => 1,
                    'image' => $product->image
                ];
            }

            $_SESSION['flash'] = [
                'type' => 'success',
                'message' => 'Đã thêm sản phẩm vào giỏ hàng!'
            ];            $referer = $_SERVER['HTTP_REFERER'] ?? BASE_URL . '/product/cart';
            header("Location: $referer");
        } catch (Exception $e) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ];
            header('Location: ' . BASE_URL . '/product');
        }
    }

    /**
     * Hiển thị giỏ hàng
     */
    public function cart()
    {
        $cartItems = $_SESSION['cart'] ?? [];
        $totalAmount = 0;

        // Tính tổng tiền
        foreach ($cartItems as $item) {
            $totalAmount += $item['price'] * $item['quantity'];
        }

        include 'app/views/product/cart.php';
    }

    /**
     * Cập nhật số lượng sản phẩm trong giỏ hàng
     */
    public function updateCart()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $quantities = $_POST['quantities'] ?? [];

                foreach ($quantities as $productId => $quantity) {
                    $quantity = (int)$quantity;

                    if ($quantity <= 0) {
                        // Xóa sản phẩm nếu số lượng <= 0
                        unset($_SESSION['cart'][$productId]);
                    } else {
                        // Cập nhật số lượng
                        if (isset($_SESSION['cart'][$productId])) {
                            $_SESSION['cart'][$productId]['quantity'] = $quantity;
                        }
                    }
                }

                $_SESSION['flash'] = [
                    'type' => 'success',
                    'message' => 'Đã cập nhật giỏ hàng!'
                ];
            } catch (Exception $e) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Có lỗi xảy ra khi cập nhật giỏ hàng.'
                ];
            }
        }        header('Location: ' . BASE_URL . '/product/cart');
    }

    /**
     * Xóa sản phẩm khỏi giỏ hàng
     */
    public function removeFromCart($productId)
    {
        if (isset($_SESSION['cart'][$productId])) {
            unset($_SESSION['cart'][$productId]);
            $_SESSION['flash'] = [
                'type' => 'success',
                'message' => 'Đã xóa sản phẩm khỏi giỏ hàng!'
            ];
        } else {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Không tìm thấy sản phẩm trong giỏ hàng.'
            ];
        }        header('Location: ' . BASE_URL . '/product/cart');
    }

    /**
     * Hiển thị trang checkout
     */
    public function checkout()
    {
        $cartItems = $_SESSION['cart'] ?? [];

        // Kiểm tra giỏ hàng có rỗng không
        if (empty($cartItems)) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Giỏ hàng của bạn đang trống!'
            ];
            header('Location: /shopbanhang/Product/cart');
            return;
        }

        // Tính tổng tiền
        $totalAmount = 0;
        foreach ($cartItems as $item) {
            $totalAmount += $item['price'] * $item['quantity'];
        }

        include 'app/views/product/checkout.php';
    }

    /**
     * Xử lý đặt hàng
     */
    public function processCheckout()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Validation
                $customerName = trim($_POST['customer_name'] ?? '');
                $customerPhone = trim($_POST['customer_phone'] ?? '');
                $customerAddress = trim($_POST['customer_address'] ?? '');

                $errors = [];

                if (strlen($customerName) < 2) {
                    $errors[] = 'Tên khách hàng phải có ít nhất 2 ký tự.';
                }

                if (strlen($customerPhone) < 10) {
                    $errors[] = 'Số điện thoại không hợp lệ.';
                }

                if (strlen($customerAddress) < 5) {
                    $errors[] = 'Địa chỉ phải có ít nhất 5 ký tự.';
                }

                $cartItems = $_SESSION['cart'] ?? [];
                if (empty($cartItems)) {
                    $errors[] = 'Giỏ hàng đang trống.';
                }

                if (!empty($errors)) {
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => implode('<br>', $errors)
                    ];
                    header('Location: /shopbanhang/Product/checkout');
                    return;
                }                // Lấy user_id nếu đã đăng nhập
                $userId = isset($_SESSION['user']) ? $_SESSION['user']['id'] : null;
                
                // Tạo đơn hàng
                $orderId = $this->orderModel->createOrder($customerName, $customerPhone, $customerAddress, $userId);

                // Tạo chi tiết đơn hàng
                $this->orderModel->createOrderDetails($orderId, $cartItems);

                // Xóa giỏ hàng
                unset($_SESSION['cart']);

                $_SESSION['flash'] = [
                    'type' => 'success',
                    'message' => 'Đặt hàng thành công!'
                ];

                header("Location: /shopbanhang/Product/orderSuccess/$orderId");
            } catch (Exception $e) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Có lỗi xảy ra khi đặt hàng: ' . $e->getMessage()
                ];
                header('Location: /shopbanhang/Product/checkout');
            }
        } else {
            header('Location: /shopbanhang/Product/checkout');
        }
    }

    /**
     * Hiển thị trang thành công sau khi đặt hàng
     */
    public function orderSuccess($orderId)
    {
        try {
            $order = $this->orderModel->getOrderById($orderId);

            if (!$order) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Không tìm thấy đơn hàng.'
                ];
                header('Location: /shopbanhang/Product/');
                return;
            }

            include 'app/views/product/order_success.php';
        } catch (Exception $e) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ];
            header('Location: /shopbanhang/Product/');
        }
    }

    /**
     * Hiển thị trang điều khoản và dịch vụ
     */
    public function terms()
    {
        include 'app/views/product/terms.php';
    }}
?>