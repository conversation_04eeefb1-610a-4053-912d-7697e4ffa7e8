<?php

require_once('app/models/AccountModel.php');

class AccountController {
    private $accountModel;

    public function __construct() {
        $this->accountModel = new AccountModel();
    }

    /**
     * Hiển thị form đăng ký
     */
    public function register() {
        // Nếu đã đăng nhập thì chuyển về trang chủ
        if (isset($_SESSION['user'])) {
            header('Location: /shopbanhang/Product/');
            return;
        }
        
        include 'app/views/account/register.php';
    }

    /**
     * Hiển thị form đăng nhập
     */
    public function login() {
        // Nếu đã đăng nhập thì chuyển về trang chủ
        if (isset($_SESSION['user'])) {
            header('Location: /shopbanhang/Product/');
            return;
        }
        
        include 'app/views/account/login.php';
    }

    /**
     * Xử lý đăng ký tài khoản mới
     */
    public function save() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Lấy dữ liệu từ form
                $username = trim($_POST['username'] ?? '');
                $fullname = trim($_POST['fullname'] ?? '');
                $password = trim($_POST['password'] ?? '');
                $confirmPassword = trim($_POST['confirm_password'] ?? '');
                
                // Validation
                $errors = [];
                
                if (empty($username)) {
                    $errors[] = 'Username không được để trống';
                } elseif (strlen($username) < 3) {
                    $errors[] = 'Username phải có ít nhất 3 ký tự';
                } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
                    $errors[] = 'Username chỉ được chứa chữ cái, số và dấu gạch dưới';
                }
                
                if (empty($fullname)) {
                    $errors[] = 'Họ tên không được để trống';
                } elseif (strlen($fullname) < 2) {
                    $errors[] = 'Họ tên phải có ít nhất 2 ký tự';
                }
                
                if (empty($password)) {
                    $errors[] = 'Mật khẩu không được để trống';
                } elseif (strlen($password) < 6) {
                    $errors[] = 'Mật khẩu phải có ít nhất 6 ký tự';
                }
                
                if ($password !== $confirmPassword) {
                    $errors[] = 'Xác nhận mật khẩu không khớp';
                }
                
                if (!empty($errors)) {
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => implode('<br>', $errors)
                    ];
                    $_SESSION['form_data'] = $_POST; // Lưu dữ liệu form để hiển thị lại
                    header('Location: /shopbanhang/Account/register');
                    return;
                }
                
                // Tạo tài khoản mới
                $result = $this->accountModel->save($username, $fullname, $password, 'user');
                
                if ($result) {
                    $_SESSION['flash'] = [
                        'type' => 'success',
                        'message' => 'Đăng ký thành công! Bạn có thể đăng nhập ngay bây giờ.'
                    ];
                    unset($_SESSION['form_data']); // Xóa dữ liệu form
                    header('Location: /shopbanhang/Account/login');
                } else {
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => 'Có lỗi xảy ra khi đăng ký. Vui lòng thử lại.'
                    ];
                    header('Location: /shopbanhang/Account/register');
                }
                
            } catch (Exception $e) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => $e->getMessage()
                ];
                $_SESSION['form_data'] = $_POST;
                header('Location: /shopbanhang/Account/register');
            }
        } else {
            header('Location: /shopbanhang/Account/register');
        }
    }

    /**
     * Xử lý đăng nhập
     */
    public function checkLogin() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Lấy dữ liệu từ form
                $username = trim($_POST['username'] ?? '');
                $password = trim($_POST['password'] ?? '');
                
                // Validation cơ bản
                if (empty($username) || empty($password)) {
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => 'Vui lòng nhập đầy đủ username và mật khẩu'
                    ];
                    $_SESSION['form_data'] = ['username' => $username];
                    header('Location: /shopbanhang/Account/login');
                    return;
                }
                
                // Xác thực đăng nhập
                $account = $this->accountModel->authenticate($username, $password);
                
                if ($account) {
                    // Đăng nhập thành công
                    $_SESSION['user'] = [
                        'id' => $account['id'],
                        'username' => $account['username'],
                        'fullname' => $account['fullname'],
                        'role' => $account['role']
                    ];
                    
                    $_SESSION['flash'] = [
                        'type' => 'success',
                        'message' => 'Đăng nhập thành công! Chào mừng ' . htmlspecialchars($account['fullname'])
                    ];
                    
                    unset($_SESSION['form_data']);
                    
                    // Chuyển hướng về trang trước đó nếu có
                    if (isset($_SESSION['return_to'])) {
                        $return_to = $_SESSION['return_to'];
                        unset($_SESSION['return_to']);
                        header('Location: ' . $return_to);
                    } else {
                        // Nếu không có trang trước đó, chuyển hướng dựa trên role
                        if ($account['role'] === 'admin') {
                            header('Location: /shopbanhang/Product/');
                        } else {
                            header('Location: /shopbanhang/Product/');
                        }
                    }
                } else {
                    // Đăng nhập thất bại
                    $_SESSION['flash'] = [
                        'type' => 'error',
                        'message' => 'Username hoặc mật khẩu không đúng'
                    ];
                    $_SESSION['form_data'] = ['username' => $username];
                    header('Location: /shopbanhang/Account/login');
                }
                
            } catch (Exception $e) {
                $_SESSION['flash'] = [
                    'type' => 'error',
                    'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
                ];
                $_SESSION['form_data'] = ['username' => $username ?? ''];
                header('Location: /shopbanhang/Account/login');
            }
        } else {
            header('Location: /shopbanhang/Account/login');
        }
    }

    /**
     * Đăng xuất
     */
    public function logout() {
        // Xóa session user
        unset($_SESSION['user']);
        
        // Có thể xóa toàn bộ session hoặc chỉ user session
        // session_destroy(); // Xóa toàn bộ (bao gồm cả giỏ hàng)
        
        $_SESSION['flash'] = [
            'type' => 'success',
            'message' => 'Đăng xuất thành công!'
        ];
        
        header('Location: /shopbanhang/Product/');
    }

    /**
     * Kiểm tra quyền admin
     */
    private function requireAdmin() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Bạn không có quyền truy cập trang này'
            ];
            header('Location: /shopbanhang/Product/');
            exit;
        }
    }

    /**
     * Kiểm tra đã đăng nhập
     */
    private function requireLogin() {
        if (!isset($_SESSION['user'])) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Vui lòng đăng nhập để tiếp tục'
            ];
            header('Location: /shopbanhang/Account/login');
            exit;
        }
    }

    /**
     * Hiển thị thông tin tài khoản (cho user đã đăng nhập)
     */
    public function profile() {
        $this->requireLogin();
        
        $user = $_SESSION['user'];
        include 'app/views/account/profile.php';
    }

    /**
     * Quản lý tài khoản (cho admin)
     */
    public function manage() {
        $this->requireAdmin();
        
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $result = $this->accountModel->getAllAccounts($page, 25);
        
        $accounts = $result['accounts'];
        $totalPages = $result['total_pages'];
        $currentPage = $result['page'];
        
        include 'app/views/account/manage.php';
    }
    
    /**
     * Hiển thị form quên mật khẩu
     */
    public function forgotPassword() {
        if (isset($_SESSION['reset_username'])) {
            unset($_SESSION['reset_username']);
        }
        include 'app/views/account/forgot_password.php';
    }
    
    /**
     * Xác thực thông tin trước khi reset password
     */
    public function verifyIdentity() {
        $username = $_POST['username'] ?? '';
        
        // Kiểm tra tài khoản tồn tại
        $user = $this->accountModel->getUserByUsername($username);
        if (!$user) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Không tìm thấy tài khoản với tên đăng nhập này!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        
        // Kiểm tra xem user có đơn hàng nào không
        if (!$this->accountModel->hasOrders($username)) {
            // Nếu chưa có đơn hàng nào, cho phép reset password luôn
            $_SESSION['reset_username'] = $username;
            header('Location: /shopbanhang/Account/showResetForm');
            return;
        }
        
        // Lấy random 1 sản phẩm từ đơn hàng của user
        $correctProduct = $this->accountModel->getRandomOrderedProduct($username);
        if (!$correctProduct) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Không tìm thấy sản phẩm nào trong lịch sử đơn hàng!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
         // Lấy random 2 sản phẩm khác (không nằm trong đơn hàng của user)
        $otherProducts = $this->accountModel->getRandomUnorderedProducts($username, 2);
        
        // Random lại 3 sản phẩm với nhau
        $products = array_merge([$correctProduct], $otherProducts);
        shuffle($products);
        
        // Lưu username vào session để dùng cho bước tiếp theo
        $_SESSION['reset_username'] = $username;
        
        // Hiển thị form xác thực với 3 sản phẩm đã gộp và random
        include 'app/views/account/verify_product.php';
    }
    
    /**
     * Xác thực sản phẩm đã chọn
     */
    public function verifyProduct() {
        $username = $_POST['username'] ?? '';
        $productId = $_POST['product_id'] ?? '';
        
        if ($username !== $_SESSION['reset_username']) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Có lỗi xảy ra, vui lòng thử lại!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        
        // Kiểm tra xem sản phẩm được chọn có đúng là sản phẩm user đã từng mua không
        if ($this->accountModel->verifyUserProduct($username, $productId)) {
            header('Location: /shopbanhang/Account/showResetForm');
        } else {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Xác thực không thành công! Vui lòng chọn sản phẩm bạn đã từng đặt mua.'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
        }
    }
    
    /**
     * Hiển thị form đặt lại mật khẩu sau khi xác thực thành công
     */
    public function showResetForm() {
        if (!isset($_SESSION['reset_username'])) {
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        include 'app/views/account/reset_password.php';
    }
    
    /**
     * Xử lý đặt lại mật khẩu
     */
    public function resetPassword() {
        if (!isset($_SESSION['reset_username'])) {
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        
        $username = $_SESSION['reset_username'];
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Kiểm tra mật khẩu mới và xác nhận mật khẩu
        if ($newPassword !== $confirmPassword) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Mật khẩu mới và xác nhận mật khẩu không khớp!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        
        // Kiểm tra tài khoản tồn tại
        $user = $this->accountModel->getUserByUsername($username);
        if (!$user) {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Không tìm thấy tài khoản với tên đăng nhập này!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
            return;
        }
        
        // Đặt lại mật khẩu
        if ($this->accountModel->resetPassword($username, $newPassword)) {
            $_SESSION['flash'] = [
                'type' => 'success',
                'message' => 'Đặt lại mật khẩu thành công! Vui lòng đăng nhập với mật khẩu mới.'
            ];
            header('Location: /shopbanhang/Account/login');
        } else {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Có lỗi xảy ra khi đặt lại mật khẩu!'
            ];
            header('Location: /shopbanhang/Account/forgotPassword');
        }
    }
}