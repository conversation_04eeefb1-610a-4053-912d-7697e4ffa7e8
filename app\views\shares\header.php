<?php 
require_once 'app/helper/SessionHelper.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- CSP Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' https: 'unsafe-inline' 'unsafe-eval' data: maps.googleapis.com *.google.com *.gstatic.com;">
    <title>Quản lý sản phẩm</title>
    <!-- Google Maps API -->
    <script async src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places"></script>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Custom CSS -->
    <link href="/shopbanhang/assets/css/custom.css" rel="stylesheet">
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="/shopbanhang/Product/">
                <i class="fas fa-store"></i> Shop Online
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav"
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto">
                    <!-- Product Management -->
                    <li class="nav-item">
                        <a class="nav-link" href="/shopbanhang/Product/">
                            <i class="fas fa-box mr-1"></i> Sản phẩm
                        </a>
                    </li>                    <!-- Category Management -->
                    <?php if (SessionHelper::isAdmin()): ?>
                    <li class="nav-item">
                        <a class="nav-link" href="/shopbanhang/Category/">
                            <i class="fas fa-folder mr-1"></i> Danh mục
                        </a>
                    </li>
                    <?php endif; ?>                    <?php if (SessionHelper::isAdmin()): ?>
                    <!-- Admin Product & Category Management -->
                    <li class="nav-item">
                        <a class="nav-link" href="/shopbanhang/Product/add">
                            <i class="fas fa-plus-circle mr-1"></i> Thêm sản phẩm
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/shopbanhang/Category/add">
                            <i class="fas fa-folder-plus mr-1"></i> Thêm danh mục
                        </a>
                    </li>
                    <?php endif; ?>

                    <!-- Shopping Cart -->
                    <li class="nav-item">
                        <a class="nav-link" href="/shopbanhang/Order/myOrders">
                            <i class="fas fa-shopping-bag mr-1"></i>Đơn hàng của tôi
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link position-relative" href="/shopbanhang/Product/cart">
                            <i class="fas fa-shopping-cart mr-1"></i> Giỏ hàng
                            <?php
                            $cartCount = 0;
                            if (isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
                                foreach ($_SESSION['cart'] as $item) {
                                    $cartCount += $item['quantity'];
                                }
                            }
                            if ($cartCount > 0):
                            ?>
                                <span class="badge badge-danger position-absolute" style="top: 0; right: 0; transform: translate(25%, -25%);">
                                    <?php echo $cartCount > 99 ? '99+' : $cartCount; ?>
                                </span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>

                <!-- User Authentication -->
                <ul class="navbar-nav ml-auto">
                    <?php if (isset($_SESSION['user'])): ?>
                        <!-- User is logged in -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                               data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fas fa-user-circle mr-1"></i>
                                Xin chào, <?php echo htmlspecialchars($_SESSION['user']['fullname']); ?>
                                <?php if ($_SESSION['user']['role'] === 'admin'): ?>
                                    <span class="badge badge-warning ml-1">Admin</span>
                                <?php endif; ?>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                                <a class="dropdown-item" href="/shopbanhang/Account/profile">
                                    <i class="fas fa-user mr-2"></i>Thông tin tài khoản
                                </a>
                                <?php if ($_SESSION['user']['role'] === 'admin'): ?>
                                    <div class="dropdown-divider"></div>
                                    <h6 class="dropdown-header">Quản trị</h6>                                    <a class="dropdown-item" href="/shopbanhang/Account/manage">
                                        <i class="fas fa-users mr-2"></i>Quản lý tài khoản
                                    </a>
                                    <a class="dropdown-item" href="/shopbanhang/Order/manage">
                                        <i class="fas fa-shopping-bag mr-2"></i>Quản lý đơn hàng
                                    </a>
                                <?php endif; ?>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-danger" href="/shopbanhang/Account/logout">
                                    <i class="fas fa-sign-out-alt mr-2"></i>Đăng xuất
                                </a>
                            </div>
                        </li>
                    <?php else: ?>
                        <!-- User is not logged in -->
                        <li class="nav-item">
                            <a class="nav-link" href="/shopbanhang/Account/login">
                                <i class="fas fa-sign-in-alt mr-1"></i>Đăng nhập
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/shopbanhang/Account/register">
                                <i class="fas fa-user-plus mr-1"></i>Đăng ký
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <div class="container mt-4">