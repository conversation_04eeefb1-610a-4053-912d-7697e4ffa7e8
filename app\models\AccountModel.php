<?php

class AccountModel {
    private $pdo;

    public function __construct() {
        require_once('app/config/database.php');
        $database = new Database();
        $this->pdo = $database->getConnection();
        
        if (!$this->pdo) {
            throw new Exception("Connection failed: Unable to connect to database");
        }
        
        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }

    /**
     * Lấy thông tin tài khoản theo username
     * @param string $username Username cần tìm
     * @return array|null Thông tin tài khoản hoặc null nếu không tìm thấy
     */
    public function getAccountByUsername($username) {
        try {
            // Sanitize input
            $username = trim($username);
            
            if (empty($username)) {
                return null;
            }
            
            $sql = "SELECT id, username, fullname, password, role, created_at FROM account WHERE username = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$username]);
            
            $account = $stmt->fetch(PDO::FETCH_ASSOC);
            return $account ?: null;
            
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy thông tin tài khoản: " . $e->getMessage());
        }
    }

    /**
     * Tạo tài khoản mới
     * @param string $username Username
     * @param string $fullName Họ tên đầy đủ
     * @param string $password Mật khẩu (sẽ được mã hóa)
     * @param string $role Vai trò (admin/user)
     * @return bool True nếu thành công
     */
    public function save($username, $fullName, $password, $role = 'user') {
        try {
            // Validation và sanitization
            $username = trim($username);
            $fullName = trim($fullName);
            $password = trim($password);
            $role = trim($role);
            
            // Validate input
            $errors = $this->validateAccountData($username, $fullName, $password, $role);
            if (!empty($errors)) {
                throw new Exception(implode(', ', $errors));
            }
            
            // Kiểm tra username đã tồn tại chưa
            if ($this->isUsernameExists($username)) {
                throw new Exception("Username đã tồn tại");
            }
            
            // Mã hóa password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            // Lưu vào database
            $sql = "INSERT INTO account (username, fullname, password, role, created_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$username, $fullName, $hashedPassword, $role]);
            
            return $result;
            
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi tạo tài khoản: " . $e->getMessage());
        }
    }

    /**
     * Kiểm tra username đã tồn tại chưa
     * @param string $username Username cần kiểm tra
     * @return bool True nếu đã tồn tại
     */
    private function isUsernameExists($username) {
        try {
            $sql = "SELECT COUNT(*) as count FROM account WHERE username = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$username]);
            
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
            
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi kiểm tra username: " . $e->getMessage());
        }
    }

    /**
     * Validate dữ liệu tài khoản
     * @param string $username Username
     * @param string $fullName Họ tên
     * @param string $password Mật khẩu
     * @param string $role Vai trò
     * @return array Mảng lỗi (rỗng nếu hợp lệ)
     */
    private function validateAccountData($username, $fullName, $password, $role) {
        $errors = [];
        
        // Validate username
        if (empty($username)) {
            $errors[] = "Username không được để trống";
        } elseif (strlen($username) < 3) {
            $errors[] = "Username phải có ít nhất 3 ký tự";
        } elseif (strlen($username) > 50) {
            $errors[] = "Username không được quá 50 ký tự";
        } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
            $errors[] = "Username chỉ được chứa chữ cái, số và dấu gạch dưới";
        }
        
        // Validate fullname
        if (empty($fullName)) {
            $errors[] = "Họ tên không được để trống";
        } elseif (strlen($fullName) < 2) {
            $errors[] = "Họ tên phải có ít nhất 2 ký tự";
        } elseif (strlen($fullName) > 255) {
            $errors[] = "Họ tên không được quá 255 ký tự";
        }
        
        // Validate password
        if (empty($password)) {
            $errors[] = "Mật khẩu không được để trống";
        } elseif (strlen($password) < 6) {
            $errors[] = "Mật khẩu phải có ít nhất 6 ký tự";
        } elseif (strlen($password) > 255) {
            $errors[] = "Mật khẩu không được quá 255 ký tự";
        }
        
        // Validate role
        if (!in_array($role, ['admin', 'user'])) {
            $errors[] = "Vai trò không hợp lệ";
        }
        
        return $errors;
    }

    /**
     * Xác thực đăng nhập
     * @param string $username Username
     * @param string $password Mật khẩu
     * @return array|false Thông tin tài khoản nếu đúng, false nếu sai
     */
    public function authenticate($username, $password) {
        try {
            $account = $this->getAccountByUsername($username);
            
            if (!$account) {
                return false;
            }
            
            // Verify password
            if (password_verify($password, $account['password'])) {
                // Không trả về password
                unset($account['password']);
                return $account;
            }
            
            return false;
            
        } catch (Exception $e) {
            throw new Exception("Lỗi khi xác thực: " . $e->getMessage());
        }
    }

    /**
     * Lấy danh sách tất cả tài khoản (cho admin)
     * @param int $page Trang hiện tại
     * @param int $limit Số tài khoản trên mỗi trang
     * @return array Danh sách tài khoản và thông tin phân trang
     */
    public function getAllAccounts($page = 1, $limit = 25) {
        try {
            $offset = ($page - 1) * $limit;
            
            // Đếm tổng số tài khoản
            $countSql = "SELECT COUNT(*) as total FROM account";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute();
            $totalAccounts = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Lấy danh sách tài khoản (không lấy password)
            $sql = "SELECT id, username, fullname, role, created_at 
                    FROM account 
                    ORDER BY created_at DESC 
                    LIMIT ? OFFSET ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(1, $limit, PDO::PARAM_INT);
            $stmt->bindValue(2, $offset, PDO::PARAM_INT);
            $stmt->execute();
            $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return [
                'accounts' => $accounts,
                'total' => $totalAccounts,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalAccounts / $limit)
            ];
            
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy danh sách tài khoản: " . $e->getMessage());
        }
    }
    
    /**
     * Đặt lại mật khẩu cho tài khoản
     * @param string $username Username
     * @param string $newPassword Mật khẩu mới
     * @return bool True nếu thành công
     */
    public function resetPassword($username, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            
            $sql = "UPDATE account SET password = ? WHERE username = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$hashedPassword, $username]);
            
            return $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    /**
     * Lấy thông tin người dùng theo username (không bao gồm password)
     * @param string $username Username
     * @return array|null Thông tin người dùng hoặc null nếu không tìm thấy
     */
    public function getUserByUsername($username) {
        try {
            $sql = "SELECT id, username, fullname, role, created_at FROM account WHERE username = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$username]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return null;
        }
    }
    
    public function getRandomOrderedProduct($username) {
        try {
            $sql = "SELECT DISTINCT p.id, p.name, p.image 
                    FROM account a 
                    JOIN orders o ON a.id = o.user_id 
                    JOIN order_details od ON o.id = od.order_id 
                    JOIN product p ON od.product_id = p.id 
                    WHERE a.username = :username
                    ORDER BY RAND()
                    LIMIT 1";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return null;
        }
    }

    public function getRandomUnorderedProducts($username, $limit = 2) {
        try {
            $sql = "SELECT p.id, p.name, p.image 
                    FROM product p 
                    WHERE p.id NOT IN (
                        SELECT DISTINCT od.product_id
                        FROM account a 
                        JOIN orders o ON a.id = o.user_id 
                        JOIN order_details od ON o.id = od.order_id 
                        WHERE a.username = :username
                    )
                    ORDER BY RAND()
                    LIMIT :limit";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            return [];
        }
    }

    public function hasOrders($username) {
        try {
            $sql = "SELECT COUNT(*) as count 
                    FROM account a 
                    JOIN orders o ON a.id = o.user_id 
                    WHERE a.username = :username";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    public function verifyUserProduct($username, $productId) {
        try {
            $sql = "SELECT COUNT(*) as count 
                    FROM account a 
                    JOIN orders o ON a.id = o.user_id 
                    JOIN order_details od ON o.id = od.order_id 
                    WHERE a.username = :username AND od.product_id = :productId";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['count'] > 0;
        } catch (PDOException $e) {
            return false;
        }
    }
}
