<?php

class OrderModel {
    private $pdo;

    public function __construct() {
        require_once('app/config/database.php');
        $database = new Database();
        $this->pdo = $database->getConnection();

        if (!$this->pdo) {
            die("Connection failed: Unable to connect to database");
        }

        $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }

    /**
     * Tạo đơn hàng mới
     * @param string $customerName Tên khách hàng
     * @param string $customerPhone Số điện thoại khách hàng
     * @param string $customerAddress Địa chỉ khách hàng
     * @param int|null $userId ID của tài khoản đặt hàng (null nếu khách vãng lai)
     * @return int Order ID của đơn hàng vừa tạo
     */
    public function createOrder($customerName, $customerPhone, $customerAddress, $userId = null) {
        try {
            $sql = "INSERT INTO orders (name, phone, address, user_id, created_at) VALUES (?, ?, ?, ?, NOW())";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$customerName, $customerPhone, $customerAddress, $userId]);

            return $this->pdo->lastInsertId();
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi tạo đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Tạo chi tiết đơn hàng từ mảng cart items
     * @param int $orderId ID của đơn hàng
     * @param array $cartItems Mảng các sản phẩm trong giỏ hàng
     * @return bool True nếu thành công
     */
    public function createOrderDetails($orderId, $cartItems) {
        try {
            $sql = "INSERT INTO order_details (order_id, product_id, quantity, price) VALUES (?, ?, ?, ?)";
            $stmt = $this->pdo->prepare($sql);

            foreach ($cartItems as $productId => $item) {
                // Đảm bảo các giá trị là số
                $quantity = intval($item['quantity']);
                $price = floatval($item['price']);
                
                // Tính tổng giá dựa trên số lượng
                $totalPrice = $quantity * $price;

                $stmt->execute([
                    $orderId,
                    $productId,
                    $quantity,
                    $totalPrice  // Lưu giá đã nhân với số lượng
                ]);
            }
            return true;
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi tạo chi tiết đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Lấy thông tin đơn hàng kèm chi tiết theo ID
     * @param int $id ID của đơn hàng
     * @return array|null Thông tin đơn hàng và chi tiết
     */
    public function getOrderById($id) {
        try {
            // Lấy thông tin đơn hàng
            $sql = "SELECT * FROM orders WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$order) {
                return null;
            }

            // Lấy chi tiết đơn hàng kèm thông tin sản phẩm
            $sql = "SELECT od.*, p.name as product_name, p.image as product_image, od.price as unit_price
                    FROM order_details od
                    JOIN product p ON od.product_id = p.id
                    WHERE od.order_id = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$id]);
            $orderDetails = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Tính tổng tiền từ price đã lưu trong order_details
            $totalAmount = 0;
            foreach ($orderDetails as $detail) {
                $totalAmount += $detail['price'];
            }

            $order['details'] = $orderDetails;
            $order['total_amount'] = $totalAmount;

            return $order;
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy thông tin đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Lấy danh sách đơn hàng với phân trang (cho admin)
     * @param int $page Trang hiện tại
     * @param int $limit Số đơn hàng trên mỗi trang
     * @return array Danh sách đơn hàng và thông tin phân trang
     */
    public function getAllOrders($page = 1, $limit = 25) {
        try {
            $offset = ($page - 1) * $limit;

            // Đếm tổng số đơn hàng
            $countSql = "SELECT COUNT(*) as total FROM orders";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute();
            $totalOrders = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Lấy danh sách đơn hàng
            $sql = "SELECT o.*,
                           (SELECT SUM(od.price)
                            FROM order_details od
                            WHERE od.order_id = o.id) as total_amount
                    FROM orders o
                    ORDER BY o.created_at DESC
                    LIMIT ? OFFSET ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(1, $limit, PDO::PARAM_INT);
            $stmt->bindValue(2, $offset, PDO::PARAM_INT);
            $stmt->execute();
            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'orders' => $orders,
                'total' => $totalOrders,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalOrders / $limit)
            ];
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy danh sách đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Cập nhật trạng thái đơn hàng (để mở rộng trong tương lai)
     * @param int $orderId ID của đơn hàng
     * @param string $status Trạng thái mới
     * @return bool True nếu thành công
     */

    public function updateOrderStatus($orderId, $status) {
        try {
            $sql = "UPDATE orders SET status = ? WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$status, $orderId]);

            /*
            $sql = "UPDATE orders SET status = ? WHERE id = ?";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$status, $orderId]);
            */
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi cập nhật trạng thái đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Lấy đơn hàng của một khách hàng theo số điện thoại
     */
    public function getOrdersByPhone($phone, $page = 1, $limit = 10) {
        try {
            $offset = ($page - 1) * $limit;

            // Đếm tổng số đơn hàng của khách hàng
            $countSql = "SELECT COUNT(*) as total FROM orders WHERE phone = ?";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute([$phone]);
            $totalOrders = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Lấy danh sách đơn hàng
            $sql = "SELECT o.*,
                           (SELECT SUM(od.price)
                            FROM order_details od
                            WHERE od.order_id = o.id) as total_amount
                    FROM orders o
                    WHERE o.phone = ?
                    ORDER BY o.created_at DESC
                    LIMIT ? OFFSET ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(1, $phone);
            $stmt->bindValue(2, $limit, PDO::PARAM_INT);
            $stmt->bindValue(3, $offset, PDO::PARAM_INT);
            $stmt->execute();
            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'orders' => $orders,
                'total' => $totalOrders,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalOrders / $limit)
            ];
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy danh sách đơn hàng: " . $e->getMessage());
        }
    }

    /**
     * Lấy đơn hàng của một người dùng theo user_id
     * @param int $userId ID của người dùng
     * @param int $page Trang hiện tại 
     * @param int $limit Số đơn hàng trên mỗi trang
     * @return array Danh sách đơn hàng và thông tin phân trang
     */
    public function getOrdersByUserId($userId, $page = 1, $limit = 10) {
        try {
            $offset = ($page - 1) * $limit;

            // Đếm tổng số đơn hàng của người dùng
            $countSql = "SELECT COUNT(*) as total FROM orders WHERE user_id = ?";
            $countStmt = $this->pdo->prepare($countSql);
            $countStmt->execute([$userId]);
            $totalOrders = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Lấy danh sách đơn hàng
            $sql = "SELECT o.*,
                           (SELECT SUM(od.price)
                            FROM order_details od
                            WHERE od.order_id = o.id) as total_amount
                    FROM orders o
                    WHERE o.user_id = ?
                    ORDER BY o.created_at DESC
                    LIMIT ? OFFSET ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->bindValue(1, $userId);
            $stmt->bindValue(2, $limit, PDO::PARAM_INT);
            $stmt->bindValue(3, $offset, PDO::PARAM_INT);
            $stmt->execute();
            $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'orders' => $orders,
                'total' => $totalOrders,
                'page' => $page,
                'limit' => $limit,
                'total_pages' => ceil($totalOrders / $limit)
            ];
        } catch (PDOException $e) {
            throw new Exception("Lỗi khi lấy danh sách đơn hàng: " . $e->getMessage());
        }
    }
}
