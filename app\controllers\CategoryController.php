<?php

// Require SessionHelper and other necessary files
require_once('app/config/database.php');
require_once('app/models/CategoryModel.php');

class CategoryController
{
    private $categoryModel;
    private $db;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->categoryModel = new CategoryModel($this->db);

        // Kiểm tra quyền admin cho các action thêm/sửa/xóa
        $action = isset($_GET['action']) ? $_GET['action'] : '';
        if (in_array($action, ['add', 'edit', 'delete'])) {
            $this->requireAdmin();
        }
    }

    private function requireAdmin() {
        if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
            $_SESSION['flash'] = [
                'type' => 'error',
                'message' => 'Bạn không có quyền thực hiện thao tác này'
            ];
            header('Location: /shopbanhang/Category/');
            exit;
        }
    }

    public function index()
    {
        $categories = $this->categoryModel->getCategories();
        include 'app/views/category/list.php';
    }

    public function list()
    {
        $categories = $this->categoryModel->getCategories();
        include 'app/views/category/list.php';
    }

    public function show($id)
    {
        $category = $this->categoryModel->getCategoryById($id);
        if ($category) {
            include 'app/views/category/show.php';
        } else {
            echo "Không tìm thấy danh mục.";
        }
    }    public function add()
    {
        $this->requireAdmin();
        include 'app/views/category/add.php';
    }

    public function save()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            $result = $this->categoryModel->addCategory($name, $description);

            if (is_array($result)) {
                $errors = $result;
                include 'app/views/category/add.php';
            } else {
                header('Location: /shopbanhang/Category');
            }
        }
    }    public function edit($id)
    {
        $this->requireAdmin();
        $category = $this->categoryModel->getCategoryById($id);
        if ($category) {
            include 'app/views/category/edit.php';
        } else {
            echo "Không tìm thấy danh mục.";
        }
    }

    public function update()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'];
            $name = $_POST['name'];
            $description = $_POST['description'];

            $edit = $this->categoryModel->updateCategory($id, $name, $description);

            if ($edit) {
                header('Location: /shopbanhang/Category');
            } else {
                echo "Đã xảy ra lỗi khi lưu danh mục.";
            }
        }
    }    public function delete($id)
    {
        $this->requireAdmin();
        $result = $this->categoryModel->deleteCategory($id);

        if (!$result['error']) {
            header('Location: /shopbanhang/Category');
        } else {
            echo $result['message'];
            echo "<br><a href='/shopbanhang/Category'>Quay lại danh sách danh mục</a>";
        }
    }
}
?>