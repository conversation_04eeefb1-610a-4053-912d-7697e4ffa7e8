<?php include 'app/views/shares/header.php'; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb bg-light py-2 px-3 rounded shadow-sm">
        <li class="breadcrumb-item"><a href="/webbanhang/Category/"><i class="fas fa-home"></i> Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Category/show/<?php echo $category->id; ?>">Chi tiết danh mục</a></li>
        <li class="breadcrumb-item active" aria-current="page">Sửa danh mục</li>
    </ol>
</nav>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card shadow-lg border-0 mb-5">
            <div class="card-header text-center">
                <h2 class="mb-0"><i class="fas fa-edit mr-2"></i>Sửa danh mục</h2>
            </div>
            <div class="card-body p-4 p-lg-5">
                <form method="POST" action="/webbanhang/Category/update">
                    <input type="hidden" name="id" value="<?php echo $category->id; ?>">

                    <div class="form-group">
                        <label for="name"><i class="fas fa-tag mr-2"></i>Tên danh mục:</label>
                        <input type="text" id="name" name="name" class="form-control"
                               value="<?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="description"><i class="fas fa-align-left mr-2"></i>Mô tả:</label>
                        <textarea id="description" name="description" class="form-control" rows="5" required><?php echo htmlspecialchars($category->description, ENT_QUOTES, 'UTF-8'); ?></textarea>
                    </div>

                    <div class="form-group mt-5 text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save mr-2"></i>Lưu thay đổi
                        </button>
                        <a href="/webbanhang/Category/show/<?php echo $category->id; ?>" class="btn btn-secondary btn-lg px-5 ml-2">
                            <i class="fas fa-arrow-left mr-2"></i>Quay lại
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
