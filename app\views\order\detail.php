<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; 
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/webbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item"><a href="/webbanhang/Order/manage">Quản lý đơn hàng</a></li>
        <li class="breadcrumb-item active" aria-current="page">Chi tiết đơn hàng #<?php echo $order['id']; ?></li>
    </ol>
</nav>

<div class="card">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="fas fa-file-invoice mr-2"></i>Chi tiết đơn hàng #<?php echo $order['id']; ?>
        </h4>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <!-- Thông tin khách hàng -->
            <div class="col-md-6">
                <h5 class="mb-3">Thông tin khách hàng</h5>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Họ tên:</strong></td>
                        <td><?php echo htmlspecialchars($order['name']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Số điện thoại:</strong></td>
                        <td><?php echo htmlspecialchars($order['phone']); ?></td>
                    </tr>
                    <tr>
                        <td><strong>Địa chỉ:</strong></td>
                        <td><?php echo htmlspecialchars($order['address']); ?></td>
                    </tr>
                </table>
            </div>
            <!-- Thông tin đơn hàng -->
            <div class="col-md-6">
                <h5 class="mb-3">Thông tin đơn hàng</h5>
                <table class="table table-borderless">
                    <tr>
                        <td><strong>Ngày đặt:</strong></td>
                        <td><?php echo date('d/m/Y H:i', strtotime($order['created_at'])); ?></td>
                    </tr>                    <tr>
                        <td><strong>Trạng thái:</strong></td>
                        <td>
                            <?php if (SessionHelper::isAdmin()): ?>
                            <form action="/webbanhang/Order/updateStatus" method="POST" class="form-inline">
                                <input type="hidden" name="order_id" value="<?php echo $order['id']; ?>">
                                <input type="hidden" name="return_to" value="detail">
                                <div class="input-group">
                                    <select name="status" class="form-control">
                                        <option value="pending" <?php echo $order['status'] === 'pending' ? 'selected' : ''; ?>>
                                            Chờ xử lý
                                        </option>
                                        <option value="processing" <?php echo $order['status'] === 'processing' ? 'selected' : ''; ?>>
                                            Đang xử lý
                                        </option>
                                        <option value="shipping" <?php echo $order['status'] === 'shipping' ? 'selected' : ''; ?>>
                                            Đang giao hàng
                                        </option>
                                        <option value="completed" <?php echo $order['status'] === 'completed' ? 'selected' : ''; ?>>
                                            Đã hoàn thành
                                        </option>
                                        <option value="cancelled" <?php echo $order['status'] === 'cancelled' ? 'selected' : ''; ?>>
                                            Đã hủy
                                        </option>
                                    </select>
                                    <div class="input-group-append">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save mr-1"></i>Lưu
                                        </button>
                                    </div>
                                </div>
                            </form>
                            <?php endif; ?>
                            <?php
                                $statusClasses = [
                                    'pending' => 'badge-warning',
                                    'processing' => 'badge-info',
                                    'shipping' => 'badge-primary',
                                    'completed' => 'badge-success',
                                    'cancelled' => 'badge-danger'
                                ];
                                $statusLabels = [
                                    'pending' => 'Chờ xử lý',
                                    'processing' => 'Đang xử lý',
                                    'shipping' => 'Đang giao hàng',
                                    'completed' => 'Đã hoàn thành',
                                    'cancelled' => 'Đã hủy'
                                ];
                            ?>
                            <div class="mt-2">
                                <span class="badge <?php echo $statusClasses[$order['status']] ?? 'badge-secondary'; ?>">
                                    <?php echo $statusLabels[$order['status']] ?? 'Không xác định'; ?>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Tổng tiền:</strong></td>
                        <td class="text-danger font-weight-bold">
                            <?php echo number_format($order['total_amount'], 0, ',', '.'); ?>đ
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Chi tiết sản phẩm -->
        <h5 class="mb-3">Chi tiết sản phẩm</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Sản phẩm</th>
                        <th>Hình ảnh</th>
                        <th>Giá</th>
                        <th>Số lượng</th>
                        <th>Thành tiền</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($order['details'] as $detail): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($detail['product_name']); ?></td>
                            <td>
                                <img src="/webbanhang/<?php echo htmlspecialchars($detail['product_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($detail['product_name']); ?>"
                                     class="img-thumbnail"
                                     style="width: 50px; height: 50px; object-fit: cover;">
                            </td>
                            <td><?php echo number_format($detail['unit_price'], 0, ',', '.'); ?>đ</td>
                            <td><?php echo $detail['quantity']; ?></td>
                            <td><?php echo number_format($detail['price'], 0, ',', '.'); ?>đ</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-right"><strong>Tổng cộng:</strong></td>
                        <td class="text-danger font-weight-bold">
                            <?php echo number_format($order['total_amount'], 0, ',', '.'); ?>đ
                        </td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Nút quay lại -->
        <div class="mt-4">
            <a href="/webbanhang/Order/manage" class="btn btn-secondary">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại danh sách
            </a>
        </div>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
