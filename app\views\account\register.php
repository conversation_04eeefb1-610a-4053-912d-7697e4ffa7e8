<?php include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/webbanhang/Product/">Trang chủ</a></li>
        <li class="breadcrumb-item active" aria-current="page"><PERSON><PERSON>ng ký</li>
    </ol>
</nav>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header text-center bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-user-plus mr-2"></i>
                        Đăng ký tài khoản
                    </h3>
                </div>
                <div class="card-body p-4">
                    <form method="POST" action="/webbanhang/Account/save" class="needs-validation" novalidate>
                        <!-- Username -->
                        <div class="form-group">
                            <label for="username">Username <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                </div>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       required 
                                       minlength="3"
                                       maxlength="50"
                                       pattern="[a-zA-Z0-9_]+"
                                       value="<?php echo htmlspecialchars($_SESSION['form_data']['username'] ?? ''); ?>"
                                       placeholder="Nhập username (chỉ chữ, số và _)">
                                <div class="invalid-feedback">
                                    Username phải có 3-50 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới.
                                </div>
                            </div>
                        </div>

                        <!-- Full Name -->
                        <div class="form-group">
                            <label for="fullname">Họ và tên <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                </div>
                                <input type="text" 
                                       class="form-control" 
                                       id="fullname" 
                                       name="fullname" 
                                       required 
                                       minlength="2"
                                       maxlength="255"
                                       value="<?php echo htmlspecialchars($_SESSION['form_data']['fullname'] ?? ''); ?>"
                                       placeholder="Nhập họ và tên đầy đủ">
                                <div class="invalid-feedback">
                                    Họ tên phải có ít nhất 2 ký tự.
                                </div>
                            </div>
                        </div>

                        <!-- Password -->
                        <div class="form-group">
                            <label for="password">Mật khẩu <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       minlength="6"
                                       placeholder="Nhập mật khẩu (ít nhất 6 ký tự)">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    Mật khẩu phải có ít nhất 6 ký tự.
                                </div>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-group">
                            <label for="confirm_password">Xác nhận mật khẩu <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                </div>
                                <input type="password" 
                                       class="form-control" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       required 
                                       placeholder="Nhập lại mật khẩu">
                                <div class="invalid-feedback">
                                    Xác nhận mật khẩu không khớp.
                                </div>
                            </div>
                        </div>

                        <!-- Terms Agreement -->
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    Tôi đồng ý với <a href="/webbanhang/Product/terms" target="_blank">điều khoản và điều kiện</a> <span class="text-danger">*</span>
                                </label>
                                <div class="invalid-feedback">
                                    Bạn phải đồng ý với điều khoản và điều kiện.
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-group mb-0">
                            <button type="submit" class="btn btn-primary btn-block btn-lg">
                                <i class="fas fa-user-plus mr-2"></i>Đăng ký
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <p class="mb-0">
                        Đã có tài khoản? 
                        <a href="/webbanhang/Account/login" class="text-primary font-weight-bold">Đăng nhập ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for form validation and password toggle -->
<script>
(function() {
    'use strict';
    
    // Bootstrap validation
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                // Check password confirmation
                var password = document.getElementById('password').value;
                var confirmPassword = document.getElementById('confirm_password').value;
                
                if (password !== confirmPassword) {
                    document.getElementById('confirm_password').setCustomValidity('Mật khẩu không khớp');
                } else {
                    document.getElementById('confirm_password').setCustomValidity('');
                }
                
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
    
    // Password toggle
    document.getElementById('togglePassword').addEventListener('click', function() {
        var passwordField = document.getElementById('password');
        var icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });
    
    // Real-time password confirmation check
    document.getElementById('confirm_password').addEventListener('input', function() {
        var password = document.getElementById('password').value;
        var confirmPassword = this.value;
        
        if (password !== confirmPassword) {
            this.setCustomValidity('Mật khẩu không khớp');
        } else {
            this.setCustomValidity('');
        }
    });
})();
</script>

<?php 
// Xóa form data sau khi hiển thị
unset($_SESSION['form_data']); 
include 'app/views/shares/footer.php'; 
?>
