services:
  web:
    build: .
    ports:
      - "8080:80"
    volumes:
      - ./:/var/www/html
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=my_store
      - DB_USERNAME=root
      - DB_PASSWORD=root
    networks:
      - app-network
    depends_on:
      - mysql

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_DATABASE=my_store
      - MYSQL_ROOT_PASSWORD=root
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  db_data:
    driver: local