<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; ?>

<!-- Flash Messages -->
<?php if (isset($_SESSION['flash'])): ?>
    <div class="alert alert-<?php echo $_SESSION['flash']['type'] === 'error' ? 'danger' : 'success'; ?> alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash']['message']; ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <?php unset($_SESSION['flash']); ?>
<?php endif; ?>

<!-- Page Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-th-list mr-2"></i><PERSON>h sách sản phẩm
    </h1>
    <?php if (SessionHelper::isAdmin()): ?>
        <a href="/webbanhang/Product/add" class="btn btn-success shadow-sm">
            <i class="fas fa-plus-circle mr-2"></i>Thêm sản phẩm mới
        </a>
    <?php endif; ?>
</div>

<!-- Product Grid -->
<div class="row">
    <?php if (empty($products)): ?>
        <div class="col-12">
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>Chưa có sản phẩm nào. Hãy thêm sản phẩm mới!
            </div>
        </div>
    <?php else: ?>
        <?php foreach ($products as $product): ?>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card product-card h-100">
                    <?php if ($product->image): ?>
                        <img src="/webbanhang/<?php echo $product->image; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>">
                    <?php else: ?>
                        <img src="/webbanhang/assets/img/no-image.png" class="card-img-top" alt="No Image">
                    <?php endif; ?>
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="/webbanhang/Product/show/<?php echo $product->id; ?>">
                                <?php echo htmlspecialchars($product->name, ENT_QUOTES, 'UTF-8'); ?>
                            </a>
                        </h5>
                        <div class="product-category mb-2">
                            <i class="fas fa-tag mr-1"></i>
                            <?php echo !empty($product->category_name) ? htmlspecialchars($product->category_name, ENT_QUOTES, 'UTF-8') : 'Chưa phân loại'; ?>
                        </div>
                        <p class="card-text">
                            <?php
                                $desc = htmlspecialchars($product->description, ENT_QUOTES, 'UTF-8');
                                echo (strlen($desc) > 100) ? substr($desc, 0, 100) . '...' : $desc;
                            ?>
                        </p>
                        <div class="product-price mb-3">
                            <?php echo number_format($product->price, 0, ',', '.'); ?> VND
                        </div>                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <a href="/webbanhang/Product/show/<?php echo $product->id; ?>" class="btn btn-primary btn-sm">
                                <i class="fas fa-eye mr-1"></i>Chi tiết
                            </a>
                            <a href="/webbanhang/Product/addToCart/<?php echo $product->id; ?>" class="btn btn-success btn-sm">
                                <i class="fas fa-cart-plus mr-1"></i>Thêm vào giỏ
                            </a>
                            <a href="/webbanhang/Product/edit/<?php echo $product->id; ?>" class="btn btn-warning btn-sm mr-1">
                                    <i class="fas fa-edit mr-1"></i>Sửa
                                </a>
                                <a href="/webbanhang/Product/delete/<?php echo $product->id; ?>"
                                   class="btn btn-danger btn-sm"
                                   onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?');">
                                    <i class="fas fa-trash-alt mr-1"></i>Xóa
                                </a>
                        </div>                        <?php if (SessionHelper::isAdmin()): ?>
                            <!-- <div class="d-flex justify-content-center">
                                <a href="/webbanhang/Product/edit/<?php echo $product->id; ?>" class="btn btn-warning btn-sm mr-1">
                                    <i class="fas fa-edit mr-1"></i>Sửa
                                </a>
                                <a href="/webbanhang/Product/delete/<?php echo $product->id; ?>"
                                   class="btn btn-danger btn-sm"
                                   onclick="return confirm('Bạn có chắc chắn muốn xóa sản phẩm này?');">
                                    <i class="fas fa-trash-alt mr-1"></i>Xóa
                                </a>
                            </div> -->
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    <?php endif; ?>
</div>

<?php include 'app/views/shares/footer.php'; ?>