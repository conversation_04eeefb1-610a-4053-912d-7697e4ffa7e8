<?php 
require_once 'app/helper/SessionHelper.php';
include 'app/views/shares/header.php'; ?>

<!-- <PERSON> Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-folder mr-2"></i>Danh sách danh mục
    </h1>
    <?php if (SessionHelper::isAdmin()): ?>
        <a href="/webbanhang/Category/add" class="btn btn-success shadow-sm">
            <i class="fas fa-plus-circle mr-2"></i>Thêm danh mục mới
        </a>
    <?php endif; ?>
</div>

<!-- Category List -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-white">Quản lý danh mục sản phẩm</h6>
    </div>
    <div class="card-body">
        <?php if (empty($categories)): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle mr-2"></i>Chưa có danh mục nào. Hãy thêm danh mục mới!
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="categoryTable" width="100%" cellspacing="0">
                    <thead class="thead-light">
                        <tr>
                            <th width="5%">ID</th>
                            <th width="25%">Tên danh mục</th>
                            <th width="50%">Mô tả</th>
                            <th width="20%">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                            <tr>
                                <td><?php echo $category->id; ?></td>
                                <td>
                                    <a href="/webbanhang/Category/show/<?php echo $category->id; ?>">
                                        <?php echo htmlspecialchars($category->name, ENT_QUOTES, 'UTF-8'); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php 
                                        $desc = htmlspecialchars($category->description, ENT_QUOTES, 'UTF-8');
                                        echo (strlen($desc) > 100) ? substr($desc, 0, 100) . '...' : $desc;
                                    ?>
                                </td>
                                <td>
                                    <a href="/webbanhang/Category/show/<?php echo $category->id; ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye mr-1"></i>Chi tiết
                                    </a>
                                    <?php if (SessionHelper::isAdmin()): ?>
                                        <a href="/webbanhang/Category/edit/<?php echo $category->id; ?>" class="btn btn-warning btn-sm">
                                            <i class="fas fa-edit mr-1"></i>Sửa
                                        </a>
                                        <a href="/webbanhang/Category/delete/<?php echo $category->id; ?>" 
                                           class="btn btn-danger btn-sm"
                                           onclick="return confirm('Bạn có chắc chắn muốn xóa danh mục này?');">
                                            <i class="fas fa-trash mr-1"></i>Xóa
                                        </a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'app/views/shares/footer.php'; ?>
